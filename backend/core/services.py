#!/usr/bin/env python3
"""
Business logic services for the Doc2Dev backend
"""

from typing import List, Dict, Any, Optional
from langchain_core.vectorstores import VectorStore
from langchain_core.documents import Document

from ..core.interfaces import MetadataDBInterface, Repository

class VectorService:
    """Service for vector store operations"""
    
    def __init__(self, vector_store: VectorStore):
        """
        Initialize vector service
        
        Args:
            vector_store: Vector store instance
        """
        self.vector_store = vector_store
    
    def similarity_search(self, query: str, k: int = 4, filter: Optional[Dict] = None, **kwargs: Any) -> List[Document]:
        """
        Search for documents similar to the query
        
        Args:
            query: Query string
            k: Number of documents to return
            filter: Optional filter for document metadata
            **kwargs: Additional keyword arguments to pass to the vector store
            
        Returns:
            List of similar documents
        """
        return self.vector_store.similarity_search(
            query=query,
            k=k,
            filter=filter,
            **kwargs
        )
    
    def add_documents(self, documents: List[Document], **kwargs: Any) -> List[str]:
        """
        Add documents to the vector store
        
        Args:
            documents: List of documents to add
            **kwargs: Additional keyword arguments to pass to the vector store
            
        Returns:
            List of document IDs
        """
        return self.vector_store.add_documents(documents, **kwargs)
    
    def delete_documents(self, ids: List[str]) -> bool:
        """
        Delete documents by IDs
        
        Args:
            ids: List of document IDs to delete
            
        Returns:
            Whether the deletion was successful
        """
        try:
            self.vector_store.delete(ids)
            return True
        except Exception as e:
            print(f"Failed to delete documents: {str(e)}")
            return False

class RepositoryService:
    """Service for repository metadata operations"""
    
    def __init__(self, metadata_db: MetadataDBInterface):
        """
        Initialize repository service
        
        Args:
            metadata_db: Metadata database interface
        """
        self.metadata_db = metadata_db
    
    def get_all_repositories(self) -> List[Dict[str, Any]]:
        """
        Get all repositories
        
        Returns:
            List of repository information dictionaries
        """
        return self.metadata_db.get_repositories()
    
    def get_repository_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Get repository by name
        
        Args:
            name: Repository name
            
        Returns:
            Repository information or None if not found
        """
        results = self.metadata_db.get_repositories(field="name", value=name)
        return results[0] if results else None
    
    def get_repository_by_repo_path(self, repo_path: str) -> Optional[Dict[str, Any]]:
        """
        Get repository by repo path
        
        Args:
            repo_path: Repository path (e.g., "/owner/repo")
            
        Returns:
            Repository information or None if not found
        """
        results = self.metadata_db.get_repositories(field="repo", value=repo_path)
        return results[0] if results else None
    
    def get_repository_by_id(self, id: int) -> Optional[Dict[str, Any]]:
        """
        Get repository by ID
        
        Args:
            id: Repository ID
            
        Returns:
            Repository information or None if not found
        """
        results = self.metadata_db.get_repositories(field="id", value=id)
        return results[0] if results else None
    
    def add_repository(self, repository: Repository) -> bool:
        """
        Add a new repository
        
        Args:
            repository: Repository object
            
        Returns:
            Whether the addition was successful
        """
        return self.metadata_db.add_repository(repository)
    
    def update_repository(self, repository: Repository) -> bool:
        """
        Update repository
        
        Args:
            repository: Repository object with updated information
            
        Returns:
            Whether the update was successful
        """
        return self.metadata_db.update_repository(repository)
    
    def delete_repository(self, id: int) -> bool:
        """
        Delete repository
        
        Args:
            id: Repository ID
            
        Returns:
            Whether the deletion was successful
        """
        return self.metadata_db.delete_repository(id)
    
    def update_repository_status(self, id: int, status: str) -> bool:
        """
        Update repository status
        
        Args:
            id: Repository ID
            status: New status
            
        Returns:
            Whether the update was successful
        """
        # Get current repository data
        repo_data = self.get_repository_by_id(id)
        if not repo_data:
            return False
        
        # Create Repository object with updated status
        repository = Repository(
            id=repo_data['id'],
            name=repo_data['name'],
            repo=repo_data['repo'],
            repo_url=repo_data['repo_url'],
            description=repo_data['description'],
            repo_status=status,
            tokens=repo_data['tokens'],
            snippets=repo_data['snippets'],
            created_at=repo_data['created_at'],
            updated_at=repo_data['updated_at']
        )
        
        return self.update_repository(repository)
    
    def update_repository_counts(self, id: int, tokens: int, snippets: int) -> bool:
        """
        Update repository token and snippet counts
        
        Args:
            id: Repository ID
            tokens: Token count
            snippets: Snippet count
            
        Returns:
            Whether the update was successful
        """
        # Get current repository data
        repo_data = self.get_repository_by_id(id)
        if not repo_data:
            return False
        
        # Create Repository object with updated counts
        repository = Repository(
            id=repo_data['id'],
            name=repo_data['name'],
            repo=repo_data['repo'],
            repo_url=repo_data['repo_url'],
            description=repo_data['description'],
            repo_status=repo_data['repo_status'],
            tokens=tokens,
            snippets=snippets,
            created_at=repo_data['created_at'],
            updated_at=repo_data['updated_at']
        )
        
        return self.update_repository(repository)
    
    def delete_vector_table(self, table_name: str) -> bool:
        """
        Delete vector table
        
        Args:
            table_name: Name of the vector table to delete
            
        Returns:
            Whether the deletion was successful
        """
        return self.metadata_db.delete_vector_table(table_name)

class DocumentProcessingService:
    """Service for document processing operations"""
    
    def __init__(self, vector_service: VectorService, repository_service: RepositoryService):
        """
        Initialize document processing service
        
        Args:
            vector_service: Vector service instance
            repository_service: Repository service instance
        """
        self.vector_service = vector_service
        self.repository_service = repository_service
    
    def process_and_store_documents(self, documents: List[Document], repository_id: int) -> Dict[str, Any]:
        """
        Process and store documents in vector store
        
        Args:
            documents: List of documents to process
            repository_id: Repository ID
            
        Returns:
            Processing result with status and metadata
        """
        try:
            # Add documents to vector store
            doc_ids = self.vector_service.add_documents(documents)
            
            # Count tokens and snippets
            total_tokens = sum(len(doc.page_content.split()) for doc in documents)
            
            # Count code snippets (assuming we have a utility function)
            from ..utils.markdown import count_code_blocks_in_documents
            total_snippets = count_code_blocks_in_documents(documents)
            
            # Update repository counts
            self.repository_service.update_repository_counts(repository_id, total_tokens, total_snippets)
            
            # Update repository status to completed
            self.repository_service.update_repository_status(repository_id, "completed")
            
            return {
                "status": "success",
                "documents_processed": len(documents),
                "document_ids": doc_ids,
                "total_tokens": total_tokens,
                "total_snippets": total_snippets
            }
        
        except Exception as e:
            # Update repository status to failed
            self.repository_service.update_repository_status(repository_id, "failed")
            
            return {
                "status": "error",
                "message": str(e),
                "documents_processed": 0
            }
