#!/usr/bin/env python3
"""
Core interfaces for the Doc2Dev backend system
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

@dataclass
class Repository:
    """Repository data model"""
    name: str
    repo: str
    repo_url: str
    description: str = ''
    repo_status: str = 'pending'
    tokens: int = 0
    snippets: int = 0
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class MetadataDBInterface(ABC):
    """Abstract interface for metadata database operations"""
    
    @abstractmethod
    def get_db_connection(self):
        """Get database connection"""
        pass
    
    @abstractmethod
    def get_repositories(self, field: Optional[str] = None, value: Optional[Any] = None) -> List[Dict[str, Any]]:
        """
        Get repositories, with optional filtering
        
        Args:
            field: Optional database field to filter on
            value: Value to filter by, required if field is specified
            
        Returns:
            List of repository information dictionaries
        """
        pass
    
    @abstractmethod
    def add_repository(self, repository: Repository) -> bool:
        """
        Add a new repository
        
        Args:
            repository: Repository object containing repository information
            
        Returns:
            Whether the addition was successful
        """
        pass
    
    @abstractmethod
    def update_repository(self, repository: Repository) -> bool:
        """
        Update repository
        
        Args:
            repository: Repository object containing updated information
            
        Returns:
            Whether the update was successful
        """
        pass
    
    @abstractmethod
    def delete_repository(self, id: int) -> bool:
        """
        Delete a repository
        
        Args:
            id: Repository ID
            
        Returns:
            Whether the deletion was successful
        """
        pass
    
    @abstractmethod
    def delete_vector_table(self, table_name: str) -> bool:
        """
        Delete a vector table
        
        Args:
            table_name: Name of the vector table to delete
            
        Returns:
            Whether the deletion was successful
        """
        pass

class VectorStoreInterface(ABC):
    """Abstract interface for vector store operations"""
    
    @abstractmethod
    def similarity_search(self, query: str, k: int = 4, filter: Optional[Dict] = None, **kwargs: Any) -> List[Any]:
        """
        Search for documents similar to the query
        
        Args:
            query: Query string
            k: Number of documents to return
            filter: Optional filter for document metadata
            **kwargs: Additional keyword arguments
            
        Returns:
            List of similar documents
        """
        pass
    
    @abstractmethod
    def add_documents(self, documents: List[Any], **kwargs: Any) -> List[str]:
        """
        Add documents to the vector store
        
        Args:
            documents: List of documents to add
            **kwargs: Additional keyword arguments
            
        Returns:
            List of document IDs
        """
        pass
    
    @abstractmethod
    def delete(self, ids: List[str]) -> bool:
        """
        Delete documents by IDs
        
        Args:
            ids: List of document IDs to delete
            
        Returns:
            Whether the deletion was successful
        """
        pass

class EmbeddingInterface(ABC):
    """Abstract interface for embedding operations"""
    
    @abstractmethod
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Embed a list of documents
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embeddings
        """
        pass
    
    @abstractmethod
    def embed_query(self, text: str) -> List[float]:
        """
        Embed a single query
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector
        """
        pass

class DocumentProcessorInterface(ABC):
    """Abstract interface for document processing operations"""
    
    @abstractmethod
    def process_repository(self, repo_url: str, repo_name: str) -> Dict[str, Any]:
        """
        Process a repository and extract documents
        
        Args:
            repo_url: Repository URL
            repo_name: Repository name
            
        Returns:
            Processing result with status and metadata
        """
        pass
    
    @abstractmethod
    def extract_markdown_files(self, repo_path: str) -> List[Dict[str, Any]]:
        """
        Extract markdown files from repository
        
        Args:
            repo_path: Path to repository
            
        Returns:
            List of markdown file information
        """
        pass
    
    @abstractmethod
    def chunk_documents(self, documents: List[Any]) -> List[Any]:
        """
        Chunk documents into smaller pieces
        
        Args:
            documents: List of documents to chunk
            
        Returns:
            List of chunked documents
        """
        pass
