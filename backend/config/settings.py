#!/usr/bin/env python3
"""
Configuration management for Doc2Dev backend
"""

import os
import yaml
from typing import Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class VectorStoreConfig:
    """Vector store configuration"""
    provider: str
    connection_args: Dict[str, Any]
    table_name: str

@dataclass
class EmbeddingConfig:
    """Embedding configuration"""
    provider: str
    model: str
    api_key: Optional[str] = None

@dataclass
class MetadataDBConfig:
    """Metadata database configuration"""
    provider: str
    connection_args: Dict[str, Any]

@dataclass
class AppConfig:
    """Application configuration"""
    vector_store: VectorStoreConfig
    embedding: EmbeddingConfig
    metadata_db: MetadataDBConfig

class ConfigManager:
    """Configuration manager for the application"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize configuration manager
        
        Args:
            config_file: Path to configuration file (YAML)
        """
        self.config_file = config_file
        self._config = None
    
    def load_config(self) -> AppConfig:
        """
        Load configuration from file or environment variables
        
        Returns:
            AppConfig: Application configuration
        """
        if self._config is not None:
            return self._config
        
        if self.config_file and os.path.exists(self.config_file):
            # Load from YAML file
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            self._config = self._parse_config(config_data)
        else:
            # Load from environment variables with defaults
            self._config = self._load_default_config()
        
        return self._config
    
    def _parse_config(self, config_data: Dict[str, Any]) -> AppConfig:
        """
        Parse configuration data into AppConfig object
        
        Args:
            config_data: Raw configuration data
            
        Returns:
            AppConfig: Parsed configuration
        """
        vector_store_data = config_data.get('vector_store', {})
        embedding_data = config_data.get('embedding', {})
        metadata_db_data = config_data.get('metadata_db', {})
        
        vector_store_config = VectorStoreConfig(
            provider=vector_store_data.get('provider', 'oceanbase'),
            connection_args=vector_store_data.get('connection_args', {}),
            table_name=vector_store_data.get('table_name', 'langchain_vector')
        )
        
        embedding_config = EmbeddingConfig(
            provider=embedding_data.get('provider', 'dashscope'),
            model=embedding_data.get('model', 'text-embedding-v3'),
            api_key=embedding_data.get('api_key')
        )
        
        metadata_db_config = MetadataDBConfig(
            provider=metadata_db_data.get('provider', 'mysql'),
            connection_args=metadata_db_data.get('connection_args', {})
        )
        
        return AppConfig(
            vector_store=vector_store_config,
            embedding=embedding_config,
            metadata_db=metadata_db_config
        )
    
    def _load_default_config(self) -> AppConfig:
        """
        Load default configuration from environment variables
        
        Returns:
            AppConfig: Default configuration
        """
        # Default OceanBase connection for both vector store and metadata
        default_connection_args = {
            "host": os.getenv("DB_HOST", "127.0.0.1"),
            "port": int(os.getenv("DB_PORT", "2881")),
            "user": os.getenv("DB_USER", "root@test"),
            "password": os.getenv("DB_PASSWORD", "admin"),
            "database": os.getenv("DB_NAME", "doc2dev"),
            "charset": "utf8mb4"
        }
        
        vector_store_config = VectorStoreConfig(
            provider=os.getenv("VECTOR_STORE_PROVIDER", "oceanbase"),
            connection_args=default_connection_args.copy(),
            table_name=os.getenv("VECTOR_TABLE_NAME", "langchain_vector")
        )
        
        embedding_config = EmbeddingConfig(
            provider=os.getenv("EMBEDDING_PROVIDER", "dashscope"),
            model=os.getenv("EMBEDDING_MODEL", "text-embedding-v3"),
            api_key=os.getenv("DASHSCOPE_API_KEY")
        )
        
        metadata_db_config = MetadataDBConfig(
            provider=os.getenv("METADATA_DB_PROVIDER", "mysql"),
            connection_args=default_connection_args.copy()
        )
        
        return AppConfig(
            vector_store=vector_store_config,
            embedding=embedding_config,
            metadata_db=metadata_db_config
        )
    
    def get_config(self) -> AppConfig:
        """
        Get current configuration
        
        Returns:
            AppConfig: Current configuration
        """
        if self._config is None:
            return self.load_config()
        return self._config
    
    def reload_config(self) -> AppConfig:
        """
        Reload configuration from source
        
        Returns:
            AppConfig: Reloaded configuration
        """
        self._config = None
        return self.load_config()

# Global configuration manager instance
config_manager = ConfigManager()

def get_config() -> AppConfig:
    """
    Get application configuration
    
    Returns:
        AppConfig: Application configuration
    """
    return config_manager.get_config()

def load_config_from_file(config_file: str) -> AppConfig:
    """
    Load configuration from specific file
    
    Args:
        config_file: Path to configuration file
        
    Returns:
        AppConfig: Application configuration
    """
    manager = ConfigManager(config_file)
    return manager.load_config()
