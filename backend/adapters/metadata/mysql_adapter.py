#!/usr/bin/env python3
"""
MySQL metadata database adapter
"""

import os
import pymysql
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

@dataclass
class Repository:
    """Repository data model"""
    name: str
    repo: str
    repo_url: str
    description: str = ''
    repo_status: str = 'pending'
    tokens: int = 0
    snippets: int = 0
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class MySQLMetadataAdapter:
    """MySQL adapter for metadata database operations"""
    
    def __init__(self, connection_args: Dict[str, Any]):
        """
        Initialize MySQL metadata adapter
        
        Args:
            connection_args: Database connection arguments
        """
        self.connection_args = connection_args
    
    def get_db_connection(self):
        """
        获取数据库连接
        
        Returns:
            pymysql.Connection: 数据库连接对象
        """
        try:
            connection = pymysql.connect(**self.connection_args)
            return connection
        except Exception as e:
            print(f"数据库连接失败: {str(e)}")
            raise
    
    def get_repositories(self, field=None, value=None) -> List[Dict[str, Any]]:
        """Get repositories, with optional filtering
        
        Args:
            field: Optional database field to filter on ('name', 'repo', or 'id')
                  If None, returns all repositories
            value: Value to filter by, required if field is specified
            
        Returns:
            List[Dict]: List of repository information dictionaries
                       Returns a single-item list if field is specified and found
                       Returns empty list if no matches or on error
        """
        try:
            connection = self.get_db_connection()
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # Base SQL query
                sql = """
                SELECT id, name, description, repo, repo_url, 
                       tokens, snippets, repo_status, created_at, updated_at
                FROM repositories
                """
                
                # Determine if we're getting all repositories or filtering
                if field is not None:
                    # Validate field
                    valid_fields = ['name', 'repo', 'id']
                    if field not in valid_fields:
                        raise ValueError(f"Invalid field: {field}. Must be one of {valid_fields}")
                    
                    # Add WHERE clause for filtering
                    sql += f"WHERE {field} = %s"
                    cursor.execute(sql, (value,))
                    
                    # For single item queries, wrap result in a list for consistent return type
                    result = cursor.fetchone()
                    return [result] if result else []
                else:
                    # Get all repositories
                    sql += "ORDER BY name"
                    cursor.execute(sql)
                    return cursor.fetchall()
        except Exception as e:
            print(f"Failed to get repositories: {str(e)}")
            return []
        finally:
            if connection:
                connection.close()
    
    def add_repository(self, repository: Repository) -> bool:
        """Add a new repository using a Repository object
        
        Args:
            repository: Repository object containing repository information
            
        Returns:
            bool: Whether the addition was successful
        """
        try:
            # Convert Repository object to dictionary
            data = asdict(repository)
            
            # Verify required fields
            required_fields = ['name', 'repo', 'repo_url']
            for field in required_fields:
                if field not in data or not data[field]:
                    raise ValueError(f"Missing required field: {field}")
                    
            # Remove id, created_at, updated_at if present
            for field in ['id', 'created_at', 'updated_at']:
                if field in data:
                    del data[field]
            
            connection = self.get_db_connection()
            with connection.cursor() as cursor:
                # Dynamically build SQL insert statement
                fields_str = ", ".join(data.keys())
                placeholders = ", ".join(["%s"] * len(data))
                
                sql = f"""
                INSERT INTO repositories ({fields_str})
                VALUES ({placeholders})
                """
                
                # Execute insert
                cursor.execute(sql, tuple(data.values()))
                connection.commit()
                return True
        except Exception as e:
            print(f"Failed to add repository: {str(e)}")
            return False
        finally:
            if connection:
                connection.close()
    
    def update_repository(self, repository: Repository) -> bool:
        """Update repository using a Repository object
        
        Args:
            repository: Repository object containing updated information
            
        Returns:
            bool: Whether the update was successful
        """
        try:
            # Ensure repository has an ID
            if repository.id is None:
                raise ValueError("Repository ID is required for update")
                
            # Convert to dictionary and filter fields
            data = asdict(repository)
            valid_fields = ['name', 'description', 'repo', 'repo_url', 'repo_status', 'tokens', 'snippets']
            update_fields = {k: v for k, v in data.items() if k in valid_fields}
            
            # Ensure we have fields to update
            if not update_fields:
                raise ValueError("No valid fields provided for update")
                
            connection = self.get_db_connection()
            with connection.cursor() as cursor:
                # Dynamically build SQL update statement
                set_clause = ", ".join([f"{field} = %s" for field in update_fields.keys()])
                sql = f"""
                UPDATE repositories
                SET {set_clause}
                WHERE id = %s
                """
                
                # Create parameter tuple with values in the same order as fields in SQL
                params = tuple(update_fields.values()) + (repository.id,)
                
                # Execute update
                cursor.execute(sql, params)
                connection.commit()
                return True
        except Exception as e:
            print(f"Failed to update repository: {str(e)}")
            return False
        finally:
            if connection:
                connection.close()
    
    def delete_repository(self, id: int) -> bool:
        """Delete a repository
        
        Args:
            id: Repository ID
            
        Returns:
            bool: Whether the deletion was successful
        """
        try:
            connection = self.get_db_connection()
            with connection.cursor() as cursor:
                sql = """
                DELETE FROM repositories
                WHERE id = %s
                """
                cursor.execute(sql, (id,))
                connection.commit()
                return True
        except Exception as e:
            print(f"Failed to delete repository: {str(e)}")
            return False
        finally:
            if connection:
                connection.close()
    
    def delete_vector_table(self, table_name: str) -> bool:
        """Delete a vector table
        
        Args:
            table_name: Name of the vector table to delete
            
        Returns:
            bool: Whether the deletion was successful
        """
        try:
            connection = self.get_db_connection()
            with connection.cursor() as cursor:
                # Safely format table name
                safe_table_name = table_name.replace('-', '_')
                sql = f"DROP TABLE IF EXISTS {safe_table_name}"
                cursor.execute(sql)
                connection.commit()
                return True
        except Exception as e:
            print(f"Failed to delete vector table: {str(e)}")
            return False
        finally:
            if connection:
                connection.close()


# Default connection arguments for backward compatibility
DB_CONNECTION_ARGS = {
    "host": "127.0.0.1",
    "port": 2881,
    "user": "root@test",
    "password": "admin",
    "database": "doc2dev",
    "charset": "utf8mb4"
}

# Create default instance for backward compatibility
default_adapter = MySQLMetadataAdapter(DB_CONNECTION_ARGS)

# Export functions for backward compatibility
get_db_connection = default_adapter.get_db_connection
get_repositories = default_adapter.get_repositories
add_repository = default_adapter.add_repository
update_repository = default_adapter.update_repository
delete_repository = default_adapter.delete_repository
delete_vector_table = default_adapter.delete_vector_table
